import FrontLayout from "@/layouts/front-layout";
import { useState, useEffect, useRef } from "react";

interface ChainOption {
    id: string;
    name: string;
    icon: string;
}

interface TokenOption {
    id: string;
    symbol: string;
    icon: string;
}

export default function Index() {
    // Dropdown states
    const [fromChainDropdownOpen, setFromChainDropdownOpen] = useState(false);
    const [toChainDropdownOpen, setToChainDropdownOpen] = useState(false);
    const [fromTokenDropdownOpen, setFromTokenDropdownOpen] = useState(false);
    const [toTokenDropdownOpen, setToTokenDropdownOpen] = useState(false);

    // Ref for the form container
    const formRef = useRef<HTMLDivElement>(null);

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (formRef.current && !formRef.current.contains(event.target as Node)) {
                setFromChainDropdownOpen(false);
                setToChainDropdownOpen(false);
                setFromTokenDropdownOpen(false);
                setToTokenDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Selected values
    const [fromChain, setFromChain] = useState<ChainOption>({
        id: 'bsc',
        name: 'BSC',
        icon: 'frontend/assets/images/token/token_bnb.svg'
    });
    const [toChain, setToChain] = useState<ChainOption>({
        id: 'neonix',
        name: 'NeoNix',
        icon: 'frontend/assets/images/token/NeoNix.png'
    });
    const [fromToken, setFromToken] = useState<TokenOption>({
        id: 'usdt',
        symbol: 'USDT',
        icon: 'frontend/assets/images/token/token_usdt.svg'
    });
    const [toToken, setToToken] = useState<TokenOption>({
        id: 'usdc',
        symbol: 'USDC',
        icon: 'frontend/assets/images/token/token_usdt.svg'
    });

    // Options data
    const chainOptions: ChainOption[] = [
        {
            id: 'ethereum',
            name: 'Ethereum',
            icon: 'frontend/assets/images/token/eth_logo.png'
        },
        {
            id: 'bsc',
            name: 'BSC',
            icon: 'frontend/assets/images/token/token_bnb.svg'
        },
        {
            id: 'neonix',
            name: 'NeoNix',
            icon: 'frontend/assets/images/token/NeoNix.png'
        }
    ];

    const tokenOptions: TokenOption[] = [
        {
            id: 'usdt',
            symbol: 'USDT',
            icon: 'frontend/assets/images/token/token_usdt.svg'
        },
        {
            id: 'usdc',
            symbol: 'USDC',
            icon: 'frontend/assets/images/token/token_usdt.svg'
        }
    ];

    // Dropdown handlers
    const handleChainSelect = (chain: ChainOption, isFrom: boolean) => {
        if (isFrom) {
            setFromChain(chain);
            setFromChainDropdownOpen(false);
        } else {
            setToChain(chain);
            setToChainDropdownOpen(false);
        }
    };

    const handleTokenSelect = (token: TokenOption, isFrom: boolean) => {
        if (isFrom) {
            setFromToken(token);
            setFromTokenDropdownOpen(false);
        } else {
            setToToken(token);
            setToTokenDropdownOpen(false);
        }
    };

    return (
        <FrontLayout
            title="NeoNix - Seamless Crypto Exchange"
            description="Instantly swap Bitcoin, Ethereum, USDT, and more — without leaving your secure crypto wallet. Fast. Private. Borderless."
        >
            <section className="banner9-section">
                <div className="container">
                    <div className="row">
                        <div className="col-lg-7 text-center mx-auto">
                            <div className="banner9-left">
                                <h1 className="uppercase kufam">Seamless Crypto Exchange, Right from Your Wallet</h1>
                                <p className="inter">
                                    Instantly swap Bitcoin, Ethereum, USDT, and more — without leaving your secure
                                    crypto wallet. Fast. Private. Borderless.
                                </p>
                                <button>
                                    Connect Wallet
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section className="banner9-section">
                <div className="container">
                    <div className="row">

                        <div className="col-lg-6 mx-auto">
                            <div className="banner9-right">
                                <div className="banner9-card">
                                    <div className="card-content">
                                        <h3 className="text-center mt-4">Crypto Wallet Exchange</h3>
                                        <form>
                                            <div className="row banner9-left pt-3" ref={formRef}>
                                                <div className="swap-form-section p-0">
                                                    <div className="section-header">
                                                        <span className="section-label">From</span>
                                                        <div className={`chain-selector ${fromChainDropdownOpen ? 'active' : ''}`} onClick={() => setFromChainDropdownOpen(!fromChainDropdownOpen)}>
                                                            <img src={fromChain.icon} alt={fromChain.name}
                                                                 className="chain-icon"/>
                                                                <span className="chain-text">{fromChain.name}</span>
                                                                <span className="dropdown-arrow">▼</span>
                                                                <div className={`chain-dropdown-list ${fromChainDropdownOpen ? 'show' : ''}`}>
                                                                    <ul>
                                                                        {chainOptions.map((chain) => (
                                                                            <li key={chain.id} onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                handleChainSelect(chain, true);
                                                                            }}>
                                                                                <img src={chain.icon}
                                                                                     alt={chain.name}
                                                                                     className="chain-icon"/>
                                                                                    <span>{chain.name}</span>
                                                                            </li>
                                                                        ))}
                                                                    </ul>
                                                                </div>
                                                        </div>
                                                    </div>

                                                    <div className="token-input-container">
                                                        <div className="token-input-row">
                                                            <div className={`token-selector-left ${fromTokenDropdownOpen ? 'active' : ''}`}
                                                                 onClick={() => setFromTokenDropdownOpen(!fromTokenDropdownOpen)}>
                                                                <img src={fromToken.icon} alt={fromToken.symbol}
                                                                     className="token-icon"/>
                                                                    <span className="token-symbol">{fromToken.symbol}</span>
                                                                    <span className="dropdown-arrow">▼</span>
                                                                    <div className={`token-dropdown-list ${fromTokenDropdownOpen ? 'show' : ''}`}>
                                                                        <ul>
                                                                            {tokenOptions.map((token) => (
                                                                                <li key={token.id} onClick={(e) => {
                                                                                    e.stopPropagation();
                                                                                    handleTokenSelect(token, true);
                                                                                }}>
                                                                                    <img
                                                                                        src={token.icon}
                                                                                        alt={token.symbol}
                                                                                        className="token-icon"/>
                                                                                        <span>{token.symbol}</span>
                                                                                </li>
                                                                            ))}
                                                                        </ul>
                                                                    </div>
                                                            </div>
                                                            <div className="amount-input-section">
                                                                <input type="text" className="amount-input"
                                                                       placeholder="0" defaultValue="0"
                                                                       id="fromAmount"/>
                                                            </div>
                                                        </div>
                                                        <div className="balance-row">
                                                            <div className="balance-info">
                                                                <span className="balance-icon">Bal: </span>
                                                                <span className="balance-text">45.26</span>
                                                                <span className="max-btn">Max</span>
                                                            </div>
                                                            <div className="usd-value">$0.00</div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="swap-form-section p-0">
                                                    <div className="section-header">
                                                        <span className="section-label">To</span>
                                                        <div className={`chain-selector ${toChainDropdownOpen ? 'active' : ''}`} onClick={() => setToChainDropdownOpen(!toChainDropdownOpen)}>
                                                            <img src={toChain.icon} alt={toChain.name}
                                                                 className="chain-icon"/>
                                                                <span className="chain-text">{toChain.name}</span>
                                                                <span className="dropdown-arrow">▼</span>
                                                                <div className={`chain-dropdown-list ${toChainDropdownOpen ? 'show' : ''}`}>
                                                                    <ul>
                                                                        {chainOptions.map((chain) => (
                                                                            <li key={chain.id} onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                handleChainSelect(chain, false);
                                                                            }}>
                                                                                <img src={chain.icon}
                                                                                     alt={chain.name}
                                                                                     className="chain-icon"/>
                                                                                    <span>{chain.name}</span>
                                                                            </li>
                                                                        ))}
                                                                    </ul>
                                                                </div>
                                                        </div>
                                                    </div>

                                                    <div className="token-input-container">
                                                        <div className="token-input-row">
                                                            <div className={`token-selector-left ${toTokenDropdownOpen ? 'active' : ''}`}
                                                                 onClick={() => setToTokenDropdownOpen(!toTokenDropdownOpen)}>
                                                                <img src={toToken.icon} alt={toToken.symbol}
                                                                     className="token-icon"/>
                                                                    <span className="token-symbol">{toToken.symbol}</span>
                                                                    <span className="dropdown-arrow">▼</span>
                                                                    <div className={`token-dropdown-list ${toTokenDropdownOpen ? 'show' : ''}`}>
                                                                        <ul>
                                                                            {tokenOptions.map((token) => (
                                                                                <li key={token.id} onClick={(e) => {
                                                                                    e.stopPropagation();
                                                                                    handleTokenSelect(token, false);
                                                                                }}>
                                                                                    <img
                                                                                        src={token.icon}
                                                                                        alt={token.symbol}
                                                                                        className="token-icon"/>
                                                                                        <span>{token.symbol}</span>
                                                                                </li>
                                                                            ))}
                                                                        </ul>
                                                                    </div>
                                                            </div>
                                                            <div className="amount-input-section">
                                                                <input type="text" className="amount-input"
                                                                       placeholder="0" defaultValue="0"
                                                                       readOnly id="toAmount"/>
                                                            </div>
                                                        </div>
                                                        <div className="balance-row">
                                                            <div className="balance-info">
                                                                <span className="balance-icon">Bal️</span>
                                                                <span className="balance-text">658.55</span>
                                                            </div>
                                                            <div className="usd-value">$0.00</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="exchange-rate-info">
                                                    <span className="rate-text">1 BNB = 4560.0000 USDC</span>
                                                    <div className="fee-info">
                                                        <span className="rate-text">10 BNB = 45600.0000 USDC</span>
                                                    </div>
                                                </div>
                                                <button type="button">
                                                    Approve
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </FrontLayout>
    );
}
