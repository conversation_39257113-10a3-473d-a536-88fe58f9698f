[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "swapID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": false, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountOut", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "fromCoin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "to<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "fromChain", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "swapHash", "type": "bytes32"}], "name": "SwapCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "swapID", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "fromCoin", "type": "address"}, {"indexed": false, "internalType": "bytes32", "name": "toCoinHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "fromChain", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "name": "SwapInProgress", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "swapID", "type": "bytes32"}, {"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountOut", "type": "uint256"}, {"internalType": "address", "name": "fromCoin", "type": "address"}, {"internalType": "address", "name": "to<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "fromChain", "type": "uint256"}, {"internalType": "uint256", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "bytes32", "name": "swapHash", "type": "bytes32"}], "name": "completeCrossChainSwap", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "address", "name": "fromCoin", "type": "address"}, {"internalType": "uint256", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "name": "crossChainSwap", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_id", "type": "bytes32"}], "name": "getSwapHashFromID", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_hash", "type": "bytes32"}], "name": "getSwapIDFromHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getSwaps", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "address", "name": "_address", "type": "address"}], "name": "withdrawTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]