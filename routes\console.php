<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('once-per-minute', function () {
    $endTime=strtotime("+1 minute")-10;
    \App\Models\BlockchainTransaction::processTransactions($endTime);
    die("Completed");
})->purpose('Runs once per minute');
Artisan::command('testing', function () {
    \App\Models\BlockchainTransaction::saveNewTransaction('0x4fbcfaa7bc7a9c5be9092b9f91f1c2dcdd358fb78d90af2e8576021905b06e0b','bsc');
    die("Completed");
})->purpose('Just for testing');
