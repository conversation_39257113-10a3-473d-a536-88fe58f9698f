//don't store any confidential details in this file
module.exports = {
    NETWORKS: {
        bsc: {
            testnet: {
                config: {
                    networkSlug: 'bsc',
                    chainName: 'Binance Testnet',
                    rpcUrlHttp: 'https://data-seed-prebsc-1-s1.binance.org:8545',
                    chainId: 97,
                    token: 'BNB',
                    tokenName: 'Binance Coin',
                    blockExplorer: 'https://testnet.bscscan.com',
                },
                contracts: {
                    bridgeContract: {
                        contractAddress: '0xe820F9262319B9a0e1414a6241136f0e7D0D504B',
                    },
                    bridgeFactoryContract: {
                        contractAddress: '0xd1Cb7bC95ac184b0C68265C5Ce14050E21cBdc14',
                    },
                    bridgeCoinBUSDContract: {
                        contractAddress: '0xaB1a4d4f1D656d2450692D237fdD6C7f9146e814',
                    }
                }
            },
            mainnet: {
                config: {
                    networkSlug: 'bsc',
                    chainName: 'Binance Smart Chain',
                    rpcUrlHttp: 'https://bsc-dataseed1.binance.org',
                    chainId: 56,
                    token:'BNB',
                    tokenName: 'Binance Coin',
                    blockExplorer: 'https://bscscan.com',
                },
                contracts: {
                    bridgeContract: {
                        contractAddress: '0xxxxx',
                    },
                    bridgeFactoryContract: {
                        contractAddress: '0xxxxx',
                    },
                    bridgeCoinBUSDContract: {
                        contractAddress: '0xxxxx',
                    }
                }
            }
        },
        nnx: {
            testnet: {
                config: {
                    networkSlug: 'nnx',
                    chainName: 'NeoNix Testnet',
                    rpcUrlHttp: 'https://rpc.nnxscan.io',
                    chainId: 989890,
                    token: 'NNX',
                    tokenName: 'NeoNix',
                    blockExplorer: 'https://nnxscan.io',
                },
                contracts: {
                    bridgeContract: {
                        contractAddress: '0xe820F9262319B9a0e1414a6241136f0e7D0D504B',
                    },
                    bridgeFactoryContract: {
                        contractAddress: '0xd1Cb7bC95ac184b0C68265C5Ce14050E21cBdc14',
                    },
                    bridgeCoinBUSDContract: {
                        contractAddress: '0xe21407451BAa8e0322c9438662da0aD27E19860A',
                    }
                }
            },
            mainnet: {
                config: {
                    networkSlug: 'nnx',
                    chainName: 'NenoNix',
                    rpcUrlHttp: 'https://rpc.nnxscan.io',
                    chainId: 989890,
                    token:'NNX',
                    tokenName: 'NeoNix',
                    blockExplorer: 'https://nnxscan.io',
                },
                contracts: {
                    bridgeContract: {
                        contractAddress: '0xxxxx',
                    },
                    bridgeFactoryContract: {
                        contractAddress: '0xxxxx',
                    },
                    bridgeCoinBUSDContract: {
                        contractAddress: '0xxxxx',
                    }
                }
            }
        },
    }
}
