<?php

namespace App\Models;

use App\Components\Helper;
use App\Components\Web3NodeBackend;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BlockchainTransaction extends Model
{
    protected $fillable = [
        'address','hash','payment_hash','coin','network','expiry_time','swapID',
        'is_processed','is_correct','method','comments','raw_data','is_completed',
        'created_at','updated_at','received_amount','sent_amount','fee_amount',
    ];

    public static function saveNewTransaction($hash,$network='nnx',$address=null) {
        $model=new BlockchainTransaction();
        $model->hash=$hash;
        $model->network=trim(strtolower($network));
        if($address) {
            $model->address=$address;
        }
        $model->expiry_time=strtotime("+5 minutes");
        $check=self::query()
            ->where('hash','=',$hash)
            ->limit(1)->first();
        if($check) {
            return false;
        } else {
            if($model->save()) {
                return $model;
            }
        }
        return false;
    }

    public static function processTransactions($endTime=null) {
        if($endTime==null) {
            $endTime = strtotime("+1 minute") - 10;
        }
        if(time()>=$endTime) {
            return true;
        }
        $transactions=self::query()
            ->where('is_processed','=',0)
            ->orderByRaw("rand()")
            ->limit(10)->get();
        if(count($transactions)>0) {
            foreach ($transactions as $transaction) {
                if(!$transaction->hash || $transaction->hash=="") {
                    DB::statement("update `blockchain_transactions` set `is_processed`='1',`is_correct`='0',`comments`='Invalid hash' where `id`='" . $transaction->id . "';");
                    continue;
                }
                if(time()>=$endTime) {
                    return true;
                }
                print "Start processing blockchain #".$transaction->id.PHP_EOL;
                $transactionDetails=Web3NodeBackend::getTransactionDetails($transaction->hash,$transaction->network);
                //print_r($transactionDetails);die;
                if(is_array($transactionDetails) && count($transactionDetails)>0) {
                    $methodName=trim(strtolower($transactionDetails['methodName']));
                    switch ($methodName) {
                        case "crosschainswap":
                            $transaction->raw_data=json_encode($transactionDetails);
                            $transaction->is_processed=1;
                            $transaction->method=$transactionDetails['methodName'];
                            $transaction->address=$transactionDetails['from'];
                            $transaction->coin=$transactionDetails['fromCoin'];
                            $transaction->swapID=$transactionDetails['swapID'];
                            $transaction->updated_at=date("Y-m-d H:i:s");
                            $transaction->is_correct=1;
                            if(array_key_exists('logs',$transactionDetails)) {
                                foreach ($transactionDetails['logs'] as $log) {
                                    if(is_array($log) && count($log)===2) {
                                        if($log['0']=="amountIn") {
                                            $transaction->received_amount=round(Helper::fromWei($log[1])*1,6);
                                            if($transaction->received_amount>0) {
                                                $fee=Setting::getValue(Setting::SETTING_SWAP_FEE);
                                                if($fee>0) {
                                                    $transaction->fee_amount=round(($transaction->received_amount*$fee)/100,6);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            if($transaction->update(['raw_data','method','address','received_amount','is_correct','fee_amount','coin','swapID','is_processed','updated_at'])) {
                                $amount=0;
                                if($transaction->received_amount && $transaction->received_amount>0) {
                                    $amount=$transaction->received_amount;
                                }
                                if($amount>0) {
                                    if($transaction->fee_amount && $transaction->fee_amount>0) {
                                        $amount=round($amount-$transaction->fee_amount,6);
                                    }
                                }
                                if($amount>0 && $transaction->swapID!="") {
                                    $transaction->sent_amount=$amount;
                                    $transaction->updated_at=date("Y-m-d H:i:s");
                                    $transaction->comments="Sending amount - ".$amount;
                                    if($transaction->update(['comments','sent_amount','updated_at'])) {
                                        $transaction->sendPayment();
                                        sleep(2);
                                    }
                                } else {
                                    $transaction->updated_at=date("Y-m-d H:i:s");
                                    $transaction->comments="Invalid transaction amount - ".$amount." => swapID - ".$transaction->swapID;
                                    $transaction->update(['comments','updated_at']);
                                }
                            }
                            break;
                    }
                }
            }
            sleep(2);

            DB::statement("update `blockchain_transactions` set `is_processed`='1',`is_correct`='0',`comments`='Timeout' where `is_processed`='0' and `expiry_time`<='".time()."';");
            return self::processTransactions($endTime);
        } else {
            return true;
        }
    }

    public function sendPayment() {
        try {
            $toNetwork=(trim(strtolower($this->network))=="bsc" ? 'nnx' : 'bsc');
            $canMint=false;
            if($this->sent_amount>0) {
                if($toNetwork=="nnx") {
                    $canMint=true;
                }

                \App\Models\Log::saveLog(
                    'Start sending payment - '.$this->id,
                    'payment_send_request',
                    [
                        'network'=>$toNetwork,
                        'coin'=>$this->coin,
                        'address'=>$this->address,
                        'can_mint'=>($canMint ? 1 : 0),
                        'amount' => $this->sent_amount,
                        'wei_amount' => Helper::toWei($this->sent_amount),
                    ]
                );

                if($canMint) {
                    $transactionHash=Web3NodeBackend::mintToken($toNetwork,$this->coin,$this->address,Helper::toWei($this->sent_amount));
                    if($transactionHash) {
                        $this->payment_hash=$transactionHash;
                        $this->is_completed=1;
                        $this->updated_at=date("Y-m-d H:i:s");
                        if($this->update(['payment_hash','is_completed','updated_at'])) {
                            Web3NodeBackend::completeSwap(
                                $this->network,
                                $this->coin,
                                $this->address,
                                Helper::toWei($this->received_amount),
                                Helper::toWei($this->sent_amount),
                                $this->swapID,
                                $this->payment_hash
                            );
                        }
                    }
                } else {
                    $transactionHash=Web3NodeBackend::transferToken($toNetwork,$this->coin,$this->address,Helper::toWei($this->sent_amount));
                    if($transactionHash) {
                        $this->payment_hash=$transactionHash;
                        $this->is_completed=1;
                        $this->updated_at=date("Y-m-d H:i:s");
                        if($this->update(['payment_hash','is_completed','updated_at'])) {
                            Web3NodeBackend::completeSwap(
                                $this->network,
                                $this->coin,
                                $this->address,
                                Helper::toWei($this->received_amount),
                                Helper::toWei($this->sent_amount),
                                $this->swapID,
                                $this->payment_hash
                            );
                        }
                    }
                }
            } else {
                throw new \Exception('Invalid amount');
            }
        } catch (\Exception $e) {
            \App\Models\Log::saveLog(
                'Unable to send payment - '.$this->id,
                'payment_send_error',
                ['message'=>$e->getMessage()]
            );
        }
        return false;
    }
}
