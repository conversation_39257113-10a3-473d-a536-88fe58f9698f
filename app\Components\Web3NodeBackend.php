<?php

namespace App\Components;

class Web3NodeBackend
{
    //public access
    public static function getTransactionDetails($hash,$network)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/get-transaction-details',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'hash='.$hash.'&network='.strtolower($network),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        if(array_key_exists('result',$response)) {
                            if(array_key_exists('methodName',$response['result'])) {
                                if($response['result']['methodName']!="") {
                                    return $response['result'];
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function getCurrentBlockNumber($network)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/get-current-block-number',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'network='.strtolower($network),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['data']['blockNumber'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }

    //private access
    public static function mintToken($network,$token,$address,$weiTokenAmount)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/mint-token',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'network='.$network.'&token='.$token.'&address='.$address.'&amount='.$weiTokenAmount,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['hash'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function transferToken($network,$token,$address,$weiTokenAmount)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/transfer-token',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'network='.$network.'&token='.$token.'&address='.$address.'&amount='.$weiTokenAmount,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['hash'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
    public static function completeSwap($network,$token,$address,$weiAmountIn,$weiAmountOut,$swapID,$hash)
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => env('VITE_WEB3_API_URL').'/complete-swap',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => 'network='.$network.'&token='.$token.'&address='.$address.'&amountIn='.$weiAmountIn.'&amountOut='.$weiAmountOut.'&swapID='.$swapID.'&hash='.$hash,
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/x-www-form-urlencoded'
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        if($response!="") {
            try {
                $response=json_decode($response,true);
                if(is_array($response) && array_key_exists('status',$response)) {
                    if($response['status']=="success") {
                        return $response['hash'];
                    }
                }
            } catch (\Exception $e) {

            }
        }
        return false;
    }
}
