<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    const SETTING_SWAP_FEE = 1;

    protected $fillable = [
        'name', 'value', 'created_at', 'updated_at', 'type',
    ];

    public static function getValue($id)
    {
        $model=self::query()->where('id','=',$id)->first();
        if($model) {
            return trim($model->value);
        }
        return false;
    }
}
