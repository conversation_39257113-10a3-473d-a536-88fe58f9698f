require('dotenv').config({path: "./.env"});
const express = require('express');
const configSettings = require('./config');
const {Web3} = require('web3');
const web3Utils = require("web3-utils");
const BridgeCoinAbi = require("./Abis/BridgeCoinContract.json");
const BridgeAbi = require("./Abis/BridgeContract.json");
const BridgeFactoryAbi = require("./Abis/BridgeFactoryContract.json");
// const ContractAbi = require("./Abis/DXEStakeContract.json");
// const TokenAbi = require("./Abis/DXEContract.json");
const app = express();
const port = process.env.NODE_PORT;
const DEBUG_MODE = (process.env.DEBUG_MODE.toString().toLowerCase()=="true" ? true : false);
// const networkConfig = configSettings.NETWORKS.bsc[process.env.PROVIDER_MODE];
const cors = require('cors');
const LogsDecoder = require('logs-decoder');
const logsDecoder = LogsDecoder.create();
const InputDataDecoder = require('ethereum-input-data-decoder');
app.use(cors());

const bodyParser = require("body-parser");
let jsonParser=bodyParser.json();
let urlEncodedParser=bodyParser.urlencoded({extended: false});

const bops = require("bops");

async function fetchWeb3Provider(network) {
    let networkConfig = await getNetworkConfig(network);
    return new Web3(new Web3.providers.HttpProvider(networkConfig.config.rpcUrlHttp));
}

async function getNetworkConfig(network) {
    return configSettings.NETWORKS[network][process.env.PROVIDER_MODE];
}

//public
app.post('/get-balance',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("get-balance called");
    }
    try {
        let userAddress=req.body.address;
        let network=req.body.network;
        if(DEBUG_MODE) {
            console.log("get-balance userAddress ",userAddress);
        }
        if(userAddress!=undefined && network!=undefined) {
            const web3 = await fetchWeb3Provider(network);
            let balance = await web3.eth.getBalance(userAddress,"latest");
            if(balance) {
                balance = web3Utils.fromWei(balance,'ether');
                balance = balance * 1;
                balance = balance.toFixed(8);
                response.send({status:'success',data:{userAddress,balance}});
            } else {
                response.send({status: 'error', message: 'Not available at this moment'});
            }
        } else {
            response.send({status: 'error', message: 'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/get-token-balance',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("get-token-balance called");
    }
    try {
        let userAddress=req.body.address;
        let network=req.body.network;
        let token=req.body.token;
        if(DEBUG_MODE) {
            console.log("get-token-balance userAddress ",userAddress," token ",token);
        }
        if(userAddress!=undefined && network!=undefined && token!=undefined) {
            token = token.toString().toUpperCase().trim();
            const web3 = await fetchWeb3Provider(network);
            const networkConfig = await getNetworkConfig(network);
            const tokenContract = "bridgeCoin"+token+"Contract";

            if(networkConfig.contracts.hasOwnProperty(tokenContract)) {
                const contractInstance = new web3.eth.Contract(BridgeCoinAbi,networkConfig.contracts[tokenContract].contractAddress);
                if(contractInstance) {
                    let balance = await contractInstance.methods.balanceOf(userAddress).call();
                    balance = web3Utils.fromWei(balance,'ether');
                    balance = balance * 1;
                    balance = balance.toFixed(8);
                    response.send({status: 'success', data: {token,balance,address:userAddress, contract: contractInstance.options.address}});
                } else {
                    response.send({status:'error',message:'Something went wrong. Try later.'});
                }
            } else {
                response.send({status: 'error', message: token+' token not found'});
            }
        } else {
            response.send({status: 'error', message: 'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/get-current-block-number',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("get-current-block-number called");
    }
    try {
        if(userAddress!=undefined && network!=undefined) {
            let network = req.body.network;
            const web3 = await fetchWeb3Provider(network);
            let blockNumber = await web3.eth.getBlockNumber();
            blockNumber = blockNumber.toString();
            if (blockNumber) {
                response.send({status: 'success', data: {blockNumber}});
            } else {
                response.send({status: 'error', message: 'Not available at this moment'});
            }
        } else {
            response.send({status: 'error', message: 'Invalid request.'});
        }
    } catch (e) {
        console.log(e);
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/check-bridge-allowance',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("get-bridge-allowance called");
    }
    try {
        let userAddress=req.body.address;
        let network=req.body.network;
        let token=req.body.token;
        if(DEBUG_MODE) {
            console.log("get-bridge-allowance userAddress ",userAddress," token ",token);
        }
        if(userAddress!=undefined && network!=undefined && token!=undefined) {
            token = token.toString().toUpperCase().trim();
            const web3 = await fetchWeb3Provider(network);
            const networkConfig = await getNetworkConfig(network);
            const tokenContract = "bridgeCoin"+token+"Contract";

            if(networkConfig.contracts.hasOwnProperty(tokenContract)) {
                const contractInstance = new web3.eth.Contract(BridgeCoinAbi,networkConfig.contracts[tokenContract].contractAddress);
                if(contractInstance) {
                    let bridgeAddress = networkConfig.contracts.bridgeContract.contractAddress;
                    let allowance = await contractInstance.methods.allowance(userAddress,bridgeAddress).call();
                    allowance = web3Utils.fromWei(allowance,'ether');
                    allowance = allowance * 1;
                    allowance = allowance.toFixed(8);
                    response.send({status: 'success', data: {token,allowance,address:userAddress, contract:contractInstance.options.address, bridgeContract: bridgeAddress}});
                } else {
                    response.send({status:'error',message:'Something went wrong. Try later.'});
                }
            } else {
                response.send({status: 'error', message: token+' token not found'});
            }
        } else {
            response.send({status: 'error', message: 'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/get-transaction-details',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("get-transaction-details called");
    }
    try {
        let hash = req.body.hash;
        let network=req.body.network;
        if (DEBUG_MODE) {
            console.log("get-transaction-details hash ", hash," network ",network);
        }
        if(hash!=undefined && network!=undefined) {
            const web3 = await fetchWeb3Provider(network);
            const networkConfig = await getNetworkConfig(network);
            const newWeb3 = new Web3();
            web3.eth.getTransaction(hash).then(res=> {
                if(res==null) {
                    response.send({status:'error',message:'Invalid hash requested.'});
                } else {
                    web3.eth.getTransactionReceipt(hash).then(async receipt => {
                        let currentBlock = await web3.eth.getBlockNumber();
                        if(currentBlock) {
                            currentBlock = web3.utils.fromWei(currentBlock,'ether');
                            currentBlock = web3.utils.toWei(currentBlock,'ether');
                            currentBlock = currentBlock * 1;
                        }
                        if(DEBUG_MODE) {
                            console.log("get-transaction-details currentBlock is ",currentBlock);
                        }
                        let finalResponse={};
                        let logsDecoded = [];
                        let confirmations = 0;
                        if(res.hasOwnProperty('blockNumber')) {
                            if(currentBlock>0) {
                                let txnBlock = res.blockNumber;
                                if(txnBlock) {
                                    txnBlock = web3.utils.fromWei(txnBlock,'ether');
                                    txnBlock = web3.utils.toWei(txnBlock,'ether');
                                    txnBlock = txnBlock * 1;

                                    confirmations = currentBlock - txnBlock;
                                }
                            }
                        }
                        finalResponse['confirmations']=confirmations;
                        finalResponse['hash']=receipt.transactionHash;
                        finalResponse['to']=receipt.to;
                        finalResponse['from']=receipt.from;
                        finalResponse['network']=network;

                        let swapID = "";
                        let fromCoin = "";

                        const contractInstance = new web3.eth.Contract(BridgeAbi,networkConfig.contracts.bridgeContract.contractAddress);
                        if(contractInstance) {
                            if(DEBUG_MODE) {
                                console.log("res to address is ",res.to);
                                console.log("contract address is ",contractInstance.options.address);
                                console.log("res is ",res);
                                console.log("receipt is ",receipt);
                                console.log("receipt logs are ",receipt.logs);
                                console.log("confirmations are ",confirmations);
                            }
                            if(receipt.to.toString().toLowerCase().trim()==contractInstance.options.address.toString().toLowerCase().trim()) {
                                if(DEBUG_MODE) {
                                    console.log("get-transaction-details decode logs here");
                                }
                                let inputDecoder = new InputDataDecoder(BridgeAbi);
                                logsDecoder.addABI(BridgeAbi);
                                const input = inputDecoder.decodeData(res.input);
                                if(DEBUG_MODE) {
                                    console.log("get-transaction-details input is ",input);
                                }
                                finalResponse['methodName']=input.method;
                                if(input.method=="crossChainSwap") {
                                    if (input.hasOwnProperty('inputs')) {
                                        try {
                                            if (input.inputs.length > 0) {
                                                let logIndex = 0;
                                                for (let item of input.inputs) {
                                                    if (DEBUG_MODE) {
                                                        console.log("item is ", item);
                                                    }
                                                    let val = "";
                                                    if(logIndex===0 || logIndex===2) {
                                                        val = "0x"+item;
                                                    } else {
                                                        val = "" + parseInt(item._hex, 16).toLocaleString('fullwide', {useGrouping: false});
                                                    }
                                                    logsDecoded.push([input.names[logIndex], val]);
                                                    logIndex++;
                                                }
                                            }
                                        } catch (e) {
                                            if (DEBUG_MODE) {
                                                console.log("get-transaction-details "+input.method+" inputs decode error is ", e);
                                            }
                                        }
                                    }

                                    if(logsDecoded.length>0) {
                                        for (let i=0; i<logsDecoded.length; i++) {
                                            if(logsDecoded[i][0]=="fromCoin") {
                                                let fromCoinLog=logsDecoded[i][1];
                                                if(DEBUG_MODE) {
                                                    console.log("fromCoinLog is ",fromCoinLog);
                                                }
                                                Object.entries(networkConfig.contracts).forEach(([key, value]) => {
                                                    let contractAddress=value.contractAddress;
                                                    if(contractAddress.toString().toLowerCase().trim()==fromCoinLog.toString().toLowerCase().trim()) {
                                                        fromCoin=key.toString();
                                                        fromCoin=fromCoin.replace("bridgeCoin","");
                                                        fromCoin=fromCoin.replace("Contract","");
                                                    }
                                                });
                                            }
                                        }
                                    }

                                    if(receipt.hasOwnProperty('logs')) {
                                        for (let i=0; i<receipt.logs.length; i++) {
                                            if(receipt.logs[i].hasOwnProperty('topics')) {
                                                if(receipt.logs[i]['topics'].length==4) {
                                                    swapID=receipt.logs[i]['topics'][1];
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                finalResponse['methodName']='';
                            }
                        }
                        finalResponse['logs']=logsDecoded;
                        finalResponse['swapID']=swapID;
                        finalResponse['fromCoin']=fromCoin;
                        response.send({status:'success',result:finalResponse});
                    }).catch(error => {
                        if(DEBUG_MODE) {
                            console.log("get-transaction-details receipt error is ",error);
                        }
                        response.send({status:'error',message:'Hash details not fetched.'});
                    });
                }
            }).catch(error => {
                if(DEBUG_MODE) {
                    console.log("get-transaction-details error is ",error);
                }
                response.send({status:'error',message:'Hash details not fetched.'});
            });
        } else {
            response.send({status:'error',message:'Invalid request.'});
        }
    } catch (e) {
        if (DEBUG_MODE) {
            console.log("get-transaction-details error is ", e);
        }
        response.send({status:'error',message:'Something went wrong.'});
    }
});

//private
app.post('/create-bridge-coin',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("create-bridge-coin called");
    }
    try {
        let name = req.body.name;
        let symbol = req.body.symbol;
        let fee = req.body.fee;
        let network = req.body.network;
        if (DEBUG_MODE) {
            console.log("create-bridge-coin params ", name, symbol, fee);
        }
        if (name != undefined && symbol!=undefined && fee!=undefined) {
            const web3 = await fetchWeb3Provider(network);
            const networkConfig = await getNetworkConfig(network);
            fee = fee*1;
            const contractInstance = new web3.eth.Contract(BridgeFactoryAbi,networkConfig.contracts.bridgeFactoryContract.contractAddress);
            let encodedABI = contractInstance.methods.createBridgeCoin(name,symbol,fee).encodeABI();

            let gasPrice = await web3.eth.getGasPrice();

            const userAddr = process.env.USER_ADDR;
            const userKey = process.env.USER_PK;

            const tx = {
                to: networkConfig.contracts.bridgeFactoryContract.contractAddress,
                from: userAddr,
                gas: 2000000,
                gasPrice: gasPrice,
                //nonce: web3.eth.getTransactionCount(userAddr),
                data: encodedABI
            };

            web3.eth.accounts.signTransaction(tx, userKey)
                .then(signedTx => {
                    if(DEBUG_MODE) {
                        console.log("Signed Transaction: ", signedTx);
                    }
                    try {
                        web3.eth.sendSignedTransaction(signedTx.rawTransaction)
                            .on('transactionHash', (hash) => {
                                response.send({status: 'success', hash: hash});
                            }).catch((err3) => {
                            if(DEBUG_MODE) {
                                console.error("Error sendSignedTransaction ", err3);
                            }
                            response.send({status: 'error', message: 'Something went wrong'});
                        });
                    } catch (e) {
                        if(DEBUG_MODE) {
                            console.error("Error sending transaction: ", e);
                        }
                        response.send({status:'error',message:'Error in sending transaction.'});
                    }
                })
                .catch(err => {
                    if(DEBUG_MODE) {
                        console.error("Error signing transaction: ", err);
                    }
                    response.send({status:'error',message:'Error in signing transaction.'});
                });
        } else {
            response.send({status:'error',message:'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/add-bridge-coin',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("add-bridge-coin called");
    }
    try {
        let name = req.body.name;
        let symbol = req.body.symbol;
        let address = req.body.address;
        let network = req.body.network;
        if (DEBUG_MODE) {
            console.log("add-bridge-coin params ", name, symbol, address);
        }
        if (name != undefined && symbol!=undefined && address!=undefined) {
            const web3 = await fetchWeb3Provider(network);
            const networkConfig = await getNetworkConfig(network);
            const contractInstance = new web3.eth.Contract(BridgeFactoryAbi,networkConfig.contracts.bridgeFactoryContract.contractAddress);
            let encodedABI = contractInstance.methods.addBridgeCoin(name,symbol,address).encodeABI();

            let gasPrice = await web3.eth.getGasPrice();

            const userAddr = process.env.USER_ADDR;
            const userKey = process.env.USER_PK;

            const tx = {
                to: networkConfig.contracts.bridgeFactoryContract.contractAddress,
                from: userAddr,
                gas: 2000000,
                gasPrice: gasPrice,
                //nonce: web3.eth.getTransactionCount(userAddr),
                data: encodedABI
            };

            web3.eth.accounts.signTransaction(tx, userKey)
                .then(signedTx => {
                    if(DEBUG_MODE) {
                        console.log("Signed Transaction: ", signedTx);
                    }
                    try {
                        web3.eth.sendSignedTransaction(signedTx.rawTransaction)
                            .on('transactionHash', (hash) => {
                                response.send({status: 'success', hash: hash});
                            }).catch((err3) => {
                            if(DEBUG_MODE) {
                                console.error("Error sendSignedTransaction ", err3);
                            }
                            response.send({status: 'error', message: 'Something went wrong'});
                        });
                    } catch (e) {
                        if(DEBUG_MODE) {
                            console.error("Error sending transaction: ", e);
                        }
                        response.send({status:'error',message:'Error in sending transaction.'});
                    }
                })
                .catch(err => {
                    if(DEBUG_MODE) {
                        console.error("Error signing transaction: ", err);
                    }
                    response.send({status:'error',message:'Error in signing transaction.'});
                });
        } else {
            response.send({status:'error',message:'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/mint-token',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("mint-token called");
    }
    try {
        let amount = req.body.amount;
        let token = req.body.token;
        let address = req.body.address;
        let network = req.body.network;
        if (DEBUG_MODE) {
            console.log("mint-token params ", network, token, address, amount);
        }
        if (amount != undefined && token!=undefined && address!=undefined && network!=undefined) {
            token = token.toString().toUpperCase().trim();
            const web3 = await fetchWeb3Provider(network);
            const networkConfig = await getNetworkConfig(network);
            const coinName = "bridgeCoin"+token+"Contract";
            const contractInstance = new web3.eth.Contract(BridgeCoinAbi,networkConfig.contracts[coinName].contractAddress);
            let encodedABI = contractInstance.methods.mint(address,amount).encodeABI();

            let gasPrice = await web3.eth.getGasPrice();

            const userAddr = process.env.USER_ADDR;
            const userKey = process.env.USER_PK;

            const tx = {
                to: networkConfig.contracts[coinName].contractAddress,
                from: userAddr,
                gas: 2000000,
                gasPrice: gasPrice,
                //nonce: web3.eth.getTransactionCount(userAddr),
                data: encodedABI
            };

            web3.eth.accounts.signTransaction(tx, userKey)
                .then(signedTx => {
                    if(DEBUG_MODE) {
                        console.log("Signed Transaction: ", signedTx);
                    }
                    try {
                        web3.eth.sendSignedTransaction(signedTx.rawTransaction)
                            .on('transactionHash', (hash) => {
                                response.send({status: 'success', hash: hash});
                            }).catch((err3) => {
                            if(DEBUG_MODE) {
                                console.error("Error sendSignedTransaction ", err3);
                            }
                            response.send({status: 'error', message: 'Something went wrong'});
                        });
                    } catch (e) {
                        if(DEBUG_MODE) {
                            console.error("Error sending transaction: ", e);
                        }
                        response.send({status:'error',message:'Error in sending transaction.'});
                    }
                })
                .catch(err => {
                    if(DEBUG_MODE) {
                        console.error("Error signing transaction: ", err);
                    }
                    response.send({status:'error',message:'Error in signing transaction.'});
                });
        } else {
            response.send({status:'error',message:'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/transfer-token',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("transfer-token called");
    }
    try {
        let amount = req.body.amount;
        let token = req.body.token;
        let address = req.body.address;
        let network = req.body.network;
        if (DEBUG_MODE) {
            console.log("transfer-token params ", network, token, address, amount);
        }
        if (amount != undefined && token!=undefined && address!=undefined && network!=undefined) {
            token = token.toString().toUpperCase().trim();
            const web3 = await fetchWeb3Provider(network);
            const networkConfig = await getNetworkConfig(network);
            const coinName = "bridgeCoin"+token+"Contract";
            const tokenAddress = networkConfig.contracts[coinName].contractAddress;
            const contractInstance = new web3.eth.Contract(BridgeAbi,networkConfig.contracts.bridgeContract.contractAddress);
            let encodedABI = contractInstance.methods.withdrawTokens(tokenAddress,address,amount).encodeABI();

            let gasPrice = await web3.eth.getGasPrice();

            const userAddr = process.env.USER_ADDR;
            const userKey = process.env.USER_PK;

            const tx = {
                to: networkConfig.contracts[coinName].contractAddress,
                from: userAddr,
                gas: 2000000,
                gasPrice: gasPrice,
                //nonce: web3.eth.getTransactionCount(userAddr),
                data: encodedABI
            };

            web3.eth.accounts.signTransaction(tx, userKey)
                .then(signedTx => {
                    if(DEBUG_MODE) {
                        console.log("Signed Transaction: ", signedTx);
                    }
                    try {
                        web3.eth.sendSignedTransaction(signedTx.rawTransaction)
                            .on('transactionHash', (hash) => {
                                response.send({status: 'success', hash: hash});
                            }).catch((err3) => {
                            if(DEBUG_MODE) {
                                console.error("Error sendSignedTransaction ", err3);
                            }
                            response.send({status: 'error', message: 'Something went wrong'});
                        });
                    } catch (e) {
                        if(DEBUG_MODE) {
                            console.error("Error sending transaction: ", e);
                        }
                        response.send({status:'error',message:'Error in sending transaction.'});
                    }
                })
                .catch(err => {
                    if(DEBUG_MODE) {
                        console.error("Error signing transaction: ", err);
                    }
                    response.send({status:'error',message:'Error in signing transaction.'});
                });
        } else {
            response.send({status:'error',message:'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});
app.post('/complete-swap',urlEncodedParser, async (req, response) => {
    if(DEBUG_MODE) {
        console.log("complete-swap called");
    }
    try {
        let swapID = req.body.swapID;
        let amountIn = req.body.amountIn;
        let amountOut = req.body.amountOut;
        let token = req.body.token;
        let address = req.body.address;
        let network = req.body.network;
        let hash = req.body.hash;
        if (DEBUG_MODE) {
            console.log("complete-swap params ",swapID, network, token, address, amountIn, amountOut, hash);
        }
        if (swapID != undefined && amountIn != undefined && amountOut != undefined && token!=undefined && address!=undefined && network!=undefined && hash!=undefined) {
            token = token.toString().toUpperCase().trim();
            let toNetwork = (network=="bsc" ? 'nnx' : 'bsc');
            const web3 = await fetchWeb3Provider(network);
            const networkConfig = await getNetworkConfig(network);
            const toNetworkConfig = await getNetworkConfig(toNetwork);
            const coinName = "bridgeCoin"+token+"Contract";

            let fromCoinAddress = networkConfig.contracts[coinName].contractAddress;
            let toCoinAddress = toNetworkConfig.contracts[coinName].contractAddress;

            let fromChain = networkConfig.config.chainId;
            let toChain = toNetworkConfig.config.chainId;

            const contractInstance = new web3.eth.Contract(BridgeAbi,networkConfig.contracts.bridgeContract.contractAddress);
            let encodedABI = contractInstance.methods.completeCrossChainSwap(swapID,address,address,amountIn,amountOut,fromCoinAddress,toCoinAddress,fromChain,toChain,hash).encodeABI();

            let gasPrice = await web3.eth.getGasPrice();

            const userAddr = process.env.USER_ADDR;
            const userKey = process.env.USER_PK;

            const tx = {
                to: networkConfig.contracts.bridgeContract.contractAddress,
                from: userAddr,
                gas: 2000000,
                gasPrice: gasPrice,
                //nonce: web3.eth.getTransactionCount(userAddr),
                data: encodedABI
            };

            web3.eth.accounts.signTransaction(tx, userKey)
                .then(signedTx => {
                    if(DEBUG_MODE) {
                        console.log("Signed Transaction: ", signedTx);
                    }
                    try {
                        web3.eth.sendSignedTransaction(signedTx.rawTransaction)
                            .on('transactionHash', (hash) => {
                                response.send({status: 'success', hash: hash});
                            }).catch((err3) => {
                            if(DEBUG_MODE) {
                                console.error("Error sendSignedTransaction ", err3);
                            }
                            response.send({status: 'error', message: 'Something went wrong'});
                        });
                    } catch (e) {
                        if(DEBUG_MODE) {
                            console.error("Error sending transaction: ", e);
                        }
                        response.send({status:'error',message:'Error in sending transaction.'});
                    }
                })
                .catch(err => {
                    if(DEBUG_MODE) {
                        console.error("Error signing transaction: ", err);
                    }
                    response.send({status:'error',message:'Error in signing transaction.'});
                });
        } else {
            response.send({status:'error',message:'Invalid request.'});
        }
    } catch (e) {
        response.send({status:'error',message:'Something went wrong.'});
    }
});

app.listen(port, () => {
    console.log(`Server is running on http://localhost:${port}`);
});
