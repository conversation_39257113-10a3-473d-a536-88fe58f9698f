<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Log extends Model
{
    protected $fillable = [
        'user_id', 'particulars', 'created_at', 'updated_at', 'type','data',
    ];

    public static function saveLog($particulars,$type,$data=null,$user_id=null) {
        $model=new Log();
        $model->particulars=$particulars;
        $model->type=$type;
        if($data) {
            if(is_array($data)) {
                $data=json_encode($data);
            }
            $model->data=$data;
        }
        if($user_id) {
            $model->user_id=$user_id;
        }
        $model->created_at=date("Y-m-d H:i:s");
        if($model->save()) {
            return $model;
        }
        return null;
    }
}
