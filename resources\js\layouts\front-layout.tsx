import AuthLayoutTemplate from '@/layouts/auth/auth-simple-layout';
import {Head, <PERSON>} from "@inertiajs/react";
import AppLogoIcon from "@/components/app-logo-icon";


export default function FrontLayout({
                                        children,
                                        title,
                                        description,
                                        ...props
                                    }: { children: React.ReactNode; title: string; description: string }) {



    return (
        <>

            <Head>
                <title>NeoNix</title>
                <link rel="icon" type="image/x-icon" href="frontend/assets/images/favicon.png"/>
                <link rel="stylesheet" href="frontend/assets/css/bootstrap.min.css"/>
                <link rel="stylesheet" href="frontend/assets/css/style.css"/>
            </Head>


            <header className="header-section header9">
                <div className="container">
                    <div className="header-inner">
                        <a href={route('home')} className="logo">
                            <img src="frontend/assets/images/logo/logo.png" style={{maxWidth: '170px'}} alt="logo"/>
                        </a>
                        <div className="header-right">
                            <button type="button" className="connect-wallet-btn v9">
                                Connect <span>Wallet</span>
                            </button>
                        </div>
                    </div>
                </div>
            </header>


            {children}
        </>
    );
}
