document.addEventListener('DOMContentLoaded', function () {
    // Get form elements
    const fromAmountInput = document.getElementById('fromAmount');
    const toAmountInput = document.getElementById('toAmount');
    const swapArrowBtn = document.getElementById('swapArrow');
    const connectWalletBtn = document.getElementById('connectWallet');
    const maxBtn = document.querySelector('.max-btn');

    // Mock exchange rate (1 ETH = 2500 USDC)
    let exchangeRate = 2500;

    // Initialize dropdowns
    initializeDropdowns();

    // Handle amount input changes
    if (fromAmountInput) {
        fromAmountInput.addEventListener('input', function () {
            const amount = parseFloat(this.value) || 0;
            const convertedAmount = amount * exchangeRate;
            if (toAmountInput) {
                toAmountInput.value = convertedAmount.toFixed(4);
            }
            updateUSDValues();
        });
    }

    // Handle swap arrow click
    if (swapArrowBtn) {
        swapArrowBtn.addEventListener('click', function () {
            swapTokens();
        });
    }

    // Handle Max button click
    if (maxBtn) {
        maxBtn.addEventListener('click', function () {
            if (fromAmountInput) {
                fromAmountInput.value = '1.5';
                fromAmountInput.dispatchEvent(new Event('input'));
            }
        });
    }

    // Handle Connect Wallet button
    if (connectWalletBtn) {
        connectWalletBtn.addEventListener('click', function () {
            console.log('Connect wallet clicked');
            // Integrate with existing wallet connection logic if needed
        });
    }

    // Initialize dropdown functionality
    function initializeDropdowns() {
        // Handle chain selectors
        const chainSelectors = document.querySelectorAll('.chain-selector');
        chainSelectors.forEach(selector => {
            selector.addEventListener('click', function (e) {
                e.stopPropagation();
                closeAllDropdowns();

                const dropdownList = this.querySelector('.chain-dropdown-list');
                if (dropdownList) {
                    dropdownList.classList.toggle('show');
                    this.classList.toggle('active');
                }
            });

            // Handle chain selection
            const chainItems = selector.querySelectorAll('.chain-dropdown-list li');
            chainItems.forEach(item => {
                item.addEventListener('click', function (e) {
                    e.stopPropagation();

                    const chainIcon = this.querySelector('.chain-icon').src;
                    const chainText = this.querySelector('span').textContent;

                    // Update the selector
                    const parentSelector = this.closest('.chain-selector');
                    parentSelector.querySelector('.chain-icon').src = chainIcon;
                    parentSelector.querySelector('.chain-text').textContent = chainText;

                    // Close dropdown
                    parentSelector.querySelector('.chain-dropdown-list').classList.remove('show');
                    parentSelector.classList.remove('active');
                });
            });
        });

        // Handle token selectors
        const tokenSelectors = document.querySelectorAll('.token-selector-left');
        tokenSelectors.forEach(selector => {
            selector.addEventListener('click', function (e) {
                e.stopPropagation();
                closeAllDropdowns();

                const dropdownList = this.querySelector('.token-dropdown-list');
                if (dropdownList) {
                    dropdownList.classList.toggle('show');
                    this.classList.toggle('active');
                }
            });

            // Handle token selection
            const tokenItems = selector.querySelectorAll('.token-dropdown-list li');
            tokenItems.forEach(item => {
                item.addEventListener('click', function (e) {
                    e.stopPropagation();

                    const tokenIcon = this.querySelector('.token-icon').src;
                    const tokenSymbol = this.querySelector('span').textContent;

                    // Update the selector
                    const parentSelector = this.closest('.token-selector-left');
                    parentSelector.querySelector('.token-icon').src = tokenIcon;
                    parentSelector.querySelector('.token-symbol').textContent = tokenSymbol;

                    // Close dropdown
                    parentSelector.querySelector('.token-dropdown-list').classList.remove('show');
                    parentSelector.classList.remove('active');

                    // Update exchange rate if needed
                    updateExchangeRate();
                    updateUSDValues();
                });
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function () {
            closeAllDropdowns();
        });
    }

    // Close all dropdowns
    function closeAllDropdowns() {
        document.querySelectorAll('.chain-dropdown-list.show, .token-dropdown-list.show').forEach(dropdown => {
            dropdown.classList.remove('show');
        });
        document.querySelectorAll('.chain-selector.active, .token-selector-left.active').forEach(selector => {
            selector.classList.remove('active');
        });
    }

    // Swap tokens function
    function swapTokens() {
        const fromSection = document.querySelector('.swap-form-section:first-child');
        const toSection = document.querySelector('.swap-form-section:last-child');

        if (fromSection && toSection) {
            // Get token selectors
            const fromTokenSelector = fromSection.querySelector('.token-selector-left');
            const toTokenSelector = toSection.querySelector('.token-selector-left');

            // Swap token icons and symbols
            const fromIcon = fromTokenSelector.querySelector('.token-icon').src;
            const fromSymbol = fromTokenSelector.querySelector('.token-symbol').textContent;
            const toIcon = toTokenSelector.querySelector('.token-icon').src;
            const toSymbol = toTokenSelector.querySelector('.token-symbol').textContent;

            fromTokenSelector.querySelector('.token-icon').src = toIcon;
            fromTokenSelector.querySelector('.token-symbol').textContent = toSymbol;
            toTokenSelector.querySelector('.token-icon').src = fromIcon;
            toTokenSelector.querySelector('.token-symbol').textContent = fromSymbol;

            // Swap amounts
            const fromAmount = fromAmountInput.value;
            const toAmount = toAmountInput.value;
            fromAmountInput.value = toAmount;
            toAmountInput.value = fromAmount;

            // Update exchange rate and USD values
            updateExchangeRate();
            updateUSDValues();
        }
    }

    // Update exchange rate based on selected tokens
    function updateExchangeRate() {
        const fromToken = document.querySelector('.swap-form-section:first-child .token-symbol').textContent;
        const toToken = document.querySelector('.swap-form-section:last-child .token-symbol').textContent;

        // Mock exchange rates
        const rates = {
            'ETH-USDC': 2500,
            'ETH-USDT': 2500,
            'ETH-BNB': 8.5,
            'USDC-ETH': 0.0004,
            'USDT-ETH': 0.0004,
            'BNB-ETH': 0.118
        };

        const rateKey = `${fromToken}-${toToken}`;
        exchangeRate = rates[rateKey] || 1;

        // Recalculate amounts
        if (fromAmountInput.value) {
            fromAmountInput.dispatchEvent(new Event('input'));
        }
    }

    // Update USD values function
    function updateUSDValues() {
        const fromAmount = parseFloat(fromAmountInput?.value) || 0;
        const toAmount = parseFloat(toAmountInput?.value) || 0;

        // Mock prices
        const prices = {
            'ETH': 2500,
            'USDC': 1,
            'USDT': 1,
            'BNB': 300
        };

        const fromToken = document.querySelector('.swap-form-section:first-child .token-symbol').textContent;
        const toToken = document.querySelector('.swap-form-section:last-child .token-symbol').textContent;

        const fromUSD = fromAmount * (prices[fromToken] || 0);
        const toUSD = toAmount * (prices[toToken] || 0);

        // Update USD display values
        const fromUSDElement = document.querySelector('.swap-form-section:first-child .usd-value');
        const toUSDElement = document.querySelector('.swap-form-section:last-child .usd-value');

        if (fromUSDElement) {
            fromUSDElement.textContent = `$${fromUSD.toFixed(2)}`;
        }
        if (toUSDElement) {
            toUSDElement.textContent = `$${toUSD.toFixed(2)}`;
        }

        // Update exchange rate display
        const rateText = document.querySelector('.rate-text');
        if (rateText && fromAmount > 0) {
            const rate = toAmount / fromAmount;
            rateText.textContent = `1 ${fromToken} = ${rate.toFixed(4)} ${toToken} ($${prices[fromToken] || 0})`;
        }
    }

    // Initialize USD values
    updateUSDValues();
});
