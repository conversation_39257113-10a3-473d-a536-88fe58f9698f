<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $rows=[
            ['id'=>Setting::SETTING_SWAP_FEE,'name'=>'Swap Fee (in %)','value'=>'1','type'=>'input'],
        ];

        foreach ($rows as $row) {
            if(!Setting::query()->where('id','=',$row['id'])->first()) {
                Setting::create($row);
            }
        }
    }
}
