<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blockchain_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('address')->nullable()->index();
            $table->string('hash')->index();
            $table->string('payment_hash')->nullable()->index();
            $table->string('network')->index();
            $table->string('swapID')->nullable()->index();
            $table->string('coin')->nullable()->index();
            $table->integer('is_processed')->default(0)->index();
            $table->integer('is_completed')->default(0)->index();
            $table->integer('is_correct')->default(0)->index();
            $table->string('method')->nullable()->comment('contract method')->index();
            $table->bigInteger('expiry_time')->nullable()->index();
            $table->decimal('received_amount',48,8)->nullable()->index();
            $table->decimal('sent_amount',48,8)->nullable()->index();
            $table->decimal('fee_amount',48,8)->nullable()->index();
            $table->text('comments')->nullable();
            $table->text('raw_data')->nullable();
            $table->timestamps();

            $table->unique(['hash','network'],'network_hash');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blockchain_transactions');
    }
};
