{"name": "nodejs_web3_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"body-parser": "^1.20.2", "bops": "^1.0.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "ethereum-input-data-decoder": "^0.4.2", "express": "^4.19.2", "logs-decoder": "^3.1.4", "web3": "^4.11.0", "web3-utils": "^4.3.1"}}